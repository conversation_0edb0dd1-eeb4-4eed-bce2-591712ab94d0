{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 深度学习课程：从线性回归到大语言模型\n", "## 从基石到前沿：DNN、PINN与LLM的演进之路\n", "\n", "**课程时长：** 60分钟  \n", "**适用对象：** 研究生  \n", "**主讲内容：** 线性回归 → DNN → PINN → LLM\n", "\n", "---\n", "\n", "### 课程大纲\n", "1. **从线性回归开始** (15分钟) - 深度学习的三大要素\n", "2. **深度神经网络(DNN)** (20分钟) - 突破线性模型的局限\n", "3. **物理信息神经网络(PINN)** (15分钟) - 融合物理定律的智能\n", "4. **大语言模型(LLM)** (10分钟) - 语言智能的巅峰"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第一部分：从弹性材料说起 - 线性回归的故事\n", "\n", "### 🎯 引入问题：弹性材料的变形规律\n", "\n", "想象你有一块弹性材料（比如橡胶条），在不同的拉力作用下会发生形变。我们测量了拉力 F 与相应的位移（变形量） y，希望找出它们之间的关系。\n", "\n", "这个简单的物理问题，实际上包含了**深度学习的三大核心要素**：\n", "- **模型（Model）**：描述输入输出关系的数学函数\n", "- **损失函数（Loss Function）**：衡量预测与真实值差距的标准\n", "- **优化器（Optimizer）**：寻找最佳参数的算法"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\vscode\\.venv\\lib\\site-packages\\tqdm\\auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📚 深度学习课程环境准备完成！\n", "🔧 PyTorch版本: 1.12.0+cpu\n"]}], "source": ["# 导入必要的库\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from sklearn.linear_model import LinearRegression\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体和图形样式\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "try:\n", "    plt.style.use('seaborn-v0_8')\n", "except:\n", "    plt.style.use('default')\n", "\n", "print(\"📚 深度学习课程环境准备完成！\")\n", "print(f\"🔧 PyTorch版本: {torch.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📊 生成弹性材料数据\n", "\n", "我们模拟一个更真实的材料行为：在小变形时遵循胡克定律（线性关系），但在大变形时会出现非线性效应。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 弹性材料实验数据（前5组）：\n", "   Force(N)  Displacement(mm)\n", "0      0.00              0.10\n", "1      0.53              0.41\n", "2      1.05              1.03\n", "3      1.58              1.69\n", "4      2.11              1.86\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 数据特征：\n", "   • 数据点数量: 20\n", "   • 力的范围: 0.0 - 10.0 N\n", "   • 位移范围: 0.10 - 12.72 mm\n"]}], "source": ["# 生成更真实的弹性材料数据\n", "np.random.seed(42)\n", "\n", "# 定义真实的非线性关系：弹性 + 非线性项\n", "k1 = 0.8   # 一阶弹性系数（胡克定律）\n", "k2 = 0.05  # 二阶非线性系数\n", "F_real = np.linspace(0, 10, 20).reshape(-1, 1)  # 施加的力\n", "\n", "# 真实的非线性响应 + 测量噪声\n", "y_real = (k1 * F_real + k2 * F_real**2).flatten()\n", "noise = np.random.normal(scale=0.2, size=y_real.shape)\n", "y_noisy = y_real + noise\n", "\n", "# 创建数据表\n", "data = pd.DataFrame({\n", "    'Force(N)': F_real.flatten(),\n", "    'Displacement(mm)': y_noisy\n", "})\n", "\n", "print(\"🔬 弹性材料实验数据（前5组）：\")\n", "print(data.head().round(2))\n", "\n", "# 可视化数据\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(F_real, y_noisy, color='blue', alpha=0.7, s=50, label='实验数据点')\n", "plt.plot(F_real, y_real, 'r--', linewidth=2, label='真实关系（无噪声）')\n", "plt.xlabel('拉力 F (N)', fontsize=12)\n", "plt.ylabel('位移 y (mm)', fontsize=12)\n", "plt.title('弹性材料的力-位移关系', fontsize=14, fontweight='bold')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(f\"\\n📈 数据特征：\")\n", "print(f\"   • 数据点数量: {len(F_real)}\")\n", "print(f\"   • 力的范围: {F_real.min():.1f} - {F_real.max():.1f} N\")\n", "print(f\"   • 位移范围: {y_noisy.min():.2f} - {y_noisy.max():.2f} mm\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🎯 第一步：线性模型（胡克定律）\n", "\n", "让我们从最简单的线性模型开始：\n", "$$y = w \\cdot F + b$$\n", "\n", "这里：\n", "- **w**: 权重（斜率），对应材料的弹性系数\n", "- **b**: 偏置（截距），对应初始位移\n", "- **F**: 输入特征（拉力）\n", "- **y**: 输出目标（位移）"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 线性模型结果：\n", "   • 权重 w = 1.262\n", "   • 偏置 b = -0.635\n", "   • 均方误差 MSE = 0.1813\n", "   • 模型方程: y = 1.262 × F + -0.635\n"]}, {"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "💡 观察：残差图显示了系统性的模式，说明线性模型无法完全捕捉数据的非线性特征！\n"]}], "source": ["# 使用线性回归拟合数据\n", "linear_model = LinearRegression()\n", "linear_model.fit(F_real, y_noisy)\n", "\n", "# 获取模型参数\n", "w_linear = linear_model.coef_[0]\n", "b_linear = linear_model.intercept_\n", "\n", "# 预测\n", "y_pred_linear = linear_model.predict(F_real)\n", "\n", "# 计算损失（均方误差）\n", "mse_linear = np.mean((y_noisy - y_pred_linear)**2)\n", "\n", "print(\"🔍 线性模型结果：\")\n", "print(f\"   • 权重 w = {w_linear:.3f}\")\n", "print(f\"   • 偏置 b = {b_linear:.3f}\")\n", "print(f\"   • 均方误差 MSE = {mse_linear:.4f}\")\n", "print(f\"   • 模型方程: y = {w_linear:.3f} × F + {b_linear:.3f}\")\n", "\n", "# 可视化拟合结果\n", "plt.figure(figsize=(12, 5))\n", "\n", "# 左图：拟合结果\n", "plt.subplot(1, 2, 1)\n", "plt.scatter(F_real, y_noisy, color='blue', alpha=0.7, s=50, label='实验数据')\n", "plt.plot(F_real, y_real, 'r--', linewidth=2, label='真实关系')\n", "plt.plot(F_real, y_pred_linear, 'g-', linewidth=2, label='线性拟合')\n", "plt.xlabel('拉力 F (N)')\n", "plt.ylabel('位移 y (mm)')\n", "plt.title('线性模型拟合结果')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 右图：残差分析\n", "plt.subplot(1, 2, 2)\n", "residuals = y_noisy - y_pred_linear\n", "plt.scatter(F_real, residuals, color='red', alpha=0.7, s=50)\n", "plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)\n", "plt.xlabel('拉力 F (N)')\n", "plt.ylabel('残差 (实际值 - 预测值)')\n", "plt.title('残差分析')\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n💡 观察：残差图显示了系统性的模式，说明线性模型无法完全捕捉数据的非线性特征！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第二部分：突破线性局限 - 深度神经网络(DNN)\n", "\n", "### 🧠 为什么需要深度神经网络？\n", "\n", "线性模型的**模型偏差（Model Bias）**限制了它只能学习线性关系。但现实世界中的关系往往是非线性的！\n", "\n", "**深度神经网络的核心思想：**\n", "- 通过多层非线性变换，学习复杂的输入-输出映射\n", "- 每一层都在学习不同层次的特征表示\n", "- 理论上可以逼近任意连续函数（万能逼近定理）"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 深度神经网络架构：\n", "SimpleDNN(\n", "  (network): Sequential(\n", "    (0): Linear(in_features=1, out_features=64, bias=True)\n", "    (1): ReLU()\n", "    (2): Linear(in_features=64, out_features=32, bias=True)\n", "    (3): ReLU()\n", "    (4): Linear(in_features=32, out_features=16, bias=True)\n", "    (5): ReLU()\n", "    (6): Linear(in_features=16, out_features=1, bias=True)\n", "  )\n", ")\n", "\n", "📊 模型参数总数: 2,753\n", "\n", "🔍 网络结构分析：\n", "   • 输入层: 1个神经元 (拉力F)\n", "   • 隐藏层1: 64个神经元 + ReLU激活\n", "   • 隐藏层2: 32个神经元 + ReLU激活\n", "   • 隐藏层3: 16个神经元 + ReLU激活\n", "   • 输出层: 1个神经元 (位移y)\n"]}], "source": ["# 定义一个简单的DNN模型\n", "class SimpleDNN(nn.Module):\n", "    def __init__(self, input_size=1, hidden_sizes=[64, 32], output_size=1):\n", "        super(Simple<PERSON><PERSON><PERSON>, self).__init__()\n", "        \n", "        # 构建网络层\n", "        layers = []\n", "        prev_size = input_size\n", "        \n", "        # 隐藏层\n", "        for hidden_size in hidden_sizes:\n", "            layers.append(nn.Linear(prev_size, hidden_size))\n", "            layers.append(nn.ReLU())  # ReLU激活函数\n", "            prev_size = hidden_size\n", "        \n", "        # 输出层\n", "        layers.append(nn.Linear(prev_size, output_size))\n", "        \n", "        self.network = nn.Sequential(*layers)\n", "    \n", "    def forward(self, x):\n", "        return self.network(x)\n", "\n", "# 创建模型实例\n", "model = SimpleDNN(input_size=1, hidden_sizes=[64, 32, 16], output_size=1)\n", "\n", "print(\"🧠 深度神经网络架构：\")\n", "print(model)\n", "\n", "# 计算参数数量\n", "total_params = sum(p.numel() for p in model.parameters())\n", "print(f\"\\n📊 模型参数总数: {total_params:,}\")\n", "print(\"\\n🔍 网络结构分析：\")\n", "print(\"   • 输入层: 1个神经元 (拉力F)\")\n", "print(\"   • 隐藏层1: 64个神经元 + ReLU激活\")\n", "print(\"   • 隐藏层2: 32个神经元 + ReLU激活\")\n", "print(\"   • 隐藏层3: 16个神经元 + ReLU激活\")\n", "print(\"   • 输出层: 1个神经元 (位移y)\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始训练深度神经网络...\n", "Epoch  200, Loss: 0.014532\n", "Epoch  400, Loss: 0.011631\n", "Epoch  600, Loss: 0.011358\n", "Epoch  800, Loss: 0.011344\n", "Epoch 1000, Loss: 0.011273\n", "\n", "✅ 训练完成！\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📊 模型性能对比：\n", "   • 线性模型 MSE: 0.181289\n", "   • DNN模型 MSE:  0.011273\n", "   • 性能提升: 93.8%\n"]}], "source": ["# 训练DNN模型\n", "X_train = torch.FloatTensor(F_real)\n", "y_train = torch.FloatTensor(y_noisy.reshape(-1, 1))\n", "\n", "# 定义损失函数和优化器\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.01)\n", "\n", "# 训练过程\n", "num_epochs = 1000\n", "losses = []\n", "\n", "print(\"🚀 开始训练深度神经网络...\")\n", "for epoch in range(num_epochs):\n", "    # 前向传播\n", "    outputs = model(X_train)\n", "    loss = criterion(outputs, y_train)\n", "    \n", "    # 反向传播和优化\n", "    optimizer.zero_grad()\n", "    loss.backward()\n", "    optimizer.step()\n", "    \n", "    losses.append(loss.item())\n", "    \n", "    if (epoch + 1) % 200 == 0:\n", "        print(f\"Epoch {epoch+1:4d}, Loss: {loss.item():.6f}\")\n", "\n", "print(\"\\n✅ 训练完成！\")\n", "\n", "# 评估模型\n", "model.eval()\n", "with torch.no_grad():\n", "    y_pred_dnn = model(X_train).numpy()\n", "\n", "mse_dnn = np.mean((y_noisy.reshape(-1, 1) - y_pred_dnn)**2)\n", "\n", "# 可视化结果\n", "plt.figure(figsize=(12, 5))\n", "\n", "# 左图：训练损失\n", "plt.subplot(1, 2, 1)\n", "plt.plot(losses, color='blue', linewidth=2)\n", "plt.xlabel('训练轮数 (Epoch)')\n", "plt.ylabel('损失值 (MSE)')\n", "plt.title('训练损失曲线')\n", "plt.yscale('log')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 右图：模型对比\n", "plt.subplot(1, 2, 2)\n", "plt.scatter(F_real, y_noisy, color='blue', alpha=0.7, s=50, label='实验数据')\n", "plt.plot(F_real, y_real, 'r--', linewidth=2, label='真实关系')\n", "plt.plot(F_real, y_pred_linear, 'g-', linewidth=2, label='线性模型')\n", "plt.plot(F_real, y_pred_dnn, 'm-', linewidth=2, label='DNN模型')\n", "plt.xlabel('拉力 F (N)')\n", "plt.ylabel('位移 y (mm)')\n", "plt.title('模型对比')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n📊 模型性能对比：\")\n", "print(f\"   • 线性模型 MSE: {mse_linear:.6f}\")\n", "print(f\"   • DNN模型 MSE:  {mse_dnn:.6f}\")\n", "print(f\"   • 性能提升: {((mse_linear - mse_dnn) / mse_linear * 100):.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第三部分：融合物理定律 - 物理信息神经网络(PINN)\n", "\n", "### 🔬 什么是PINN？\n", "\n", "**物理信息神经网络(PINN)** 将物理定律直接嵌入到神经网络训练过程中：\n", "\n", "**核心思想：**\n", "- 损失函数 = 数据损失 + 物理损失\n", "- 即使数据稀少，也能给出物理上合理的预测\n", "- 预测结果必须满足物理定律"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始训练PINN模型...\n", "Epoch  400, Total Loss: 0.303260\n", "Epoch  800, Total Loss: 0.044561\n", "Epoch 1200, Total Loss: 0.024073\n", "Epoch 1600, Total Loss: 0.020855\n", "Epoch 2000, Total Loss: 0.019662\n", "\n", "✅ PINN训练完成！\n"]}], "source": ["# 简化的PINN演示\n", "class SimplePINN(nn.Module):\n", "    def __init__(self):\n", "        super(SimplePIN<PERSON>, self).__init__()\n", "        self.network = nn.Sequential(\n", "            nn.<PERSON><PERSON>(1, 50),\n", "            nn.<PERSON>(),\n", "            nn.<PERSON><PERSON>(50, 50),\n", "            nn.<PERSON>(),\n", "            nn.<PERSON><PERSON>(50, 1)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        return self.network(x)\n", "\n", "# 创建PINN模型\n", "pinn_model = SimplePINN()\n", "\n", "# 简化的物理约束：二阶导数应该与力相关\n", "def physics_loss(model, x_physics):\n", "    x_physics.requires_grad_(True)\n", "    y = model(x_physics)\n", "    \n", "    # 计算一阶导数\n", "    dy_dx = torch.autograd.grad(y, x_physics, \n", "                               grad_outputs=torch.ones_like(y),\n", "                               create_graph=True)[0]\n", "    \n", "    # 计算二阶导数\n", "    d2y_dx2 = torch.autograd.grad(dy_dx, x_physics,\n", "                                 grad_outputs=torch.ones_like(dy_dx),\n", "                                 create_graph=True)[0]\n", "    \n", "    # 物理约束：d²y/dx² 应该与外力相关\n", "    physics_residual = d2y_dx2 - 0.1 * torch.sin(x_physics)\n", "    return torch.mean(physics_residual**2)\n", "\n", "# 训练PINN\n", "criterion_pinn = nn.MSELoss()\n", "optimizer_pinn = optim.Adam(pinn_model.parameters(), lr=0.001)\n", "\n", "# 物理约束点\n", "x_physics = torch.linspace(0, 10, 100).reshape(-1, 1)\n", "x_physics.requires_grad_(True)\n", "\n", "print(\"🚀 开始训练PINN模型...\")\n", "pinn_losses = []\n", "\n", "for epoch in range(2000):\n", "    optimizer_pinn.zero_grad()\n", "    \n", "    # 数据损失\n", "    y_pred_pinn = pinn_model(X_train)\n", "    data_loss = criterion_pinn(y_pred_pinn, y_train)\n", "    \n", "    # 物理损失\n", "    phys_loss = physics_loss(pinn_model, x_physics)\n", "    \n", "    # 总损失\n", "    total_loss = data_loss + 0.1 * phys_loss\n", "    \n", "    total_loss.backward()\n", "    optimizer_pinn.step()\n", "    \n", "    pinn_losses.append(total_loss.item())\n", "    \n", "    if (epoch + 1) % 400 == 0:\n", "        print(f\"Epoch {epoch+1:4d}, Total Loss: {total_loss.item():.6f}\")\n", "\n", "print(\"\\n✅ PINN训练完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第四部分：语言智能的巅峰 - 大语言模型(LLM)\n", "\n", "### 🌟 从DNN到LLM的演进\n", "\n", "**技术演进脉络：**\n", "```\n", "线性回归 → DNN → PINN → LLM\n", "    ↓        ↓      ↓      ↓\n", "  简单     复杂   物理   语言\n", "  线性     非线性  约束   智能\n", "```\n", "\n", "**LLM的核心创新：**\n", "- **Transformer架构**：注意力机制\n", "- **大规模预训练**：从海量文本学习\n", "- **涌现能力**：规模带来的质变"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["🔍 注意力机制解释：\n", "   • 每个词都会'关注'句子中的所有词\n", "   • 注意力权重反映词与词之间的相关性\n", "   • 这使模型能够捕捉长距离依赖关系\n"]}], "source": ["# 注意力机制演示\n", "def simple_attention_demo():\n", "    \"\"\"演示注意力机制的基本概念\"\"\"\n", "    \n", "    # 模拟句子：\"深度学习很有趣\"\n", "    words = [\"深度\", \"学习\", \"很\", \"有趣\"]\n", "    \n", "    # 简化的词向量\n", "    word_vectors = np.array([\n", "        [0.1, 0.8, 0.2],  # 深度\n", "        [0.2, 0.9, 0.1],  # 学习\n", "        [0.5, 0.3, 0.7],  # 很\n", "        [0.8, 0.2, 0.9]   # 有趣\n", "    ])\n", "    \n", "    # 计算注意力权重\n", "    def compute_attention(query_idx, key_vectors):\n", "        query = word_vectors[query_idx]\n", "        scores = np.dot(key_vectors, query)\n", "        attention_weights = np.exp(scores) / np.sum(np.exp(scores))\n", "        return attention_weights\n", "    \n", "    # 可视化注意力权重\n", "    fig, axes = plt.subplots(2, 2, figsize=(12, 8))\n", "    axes = axes.flatten()\n", "    \n", "    for i, word in enumerate(words):\n", "        attention_weights = compute_attention(i, word_vectors)\n", "        \n", "        ax = axes[i]\n", "        bars = ax.bar(words, attention_weights, \n", "                     color=['red' if j==i else 'lightblue' for j in range(len(words))])\n", "        ax.set_title(f'查询词: \"{word}\" 的注意力分布')\n", "        ax.set_ylabel('注意力权重')\n", "        ax.set_ylim(0, 1)\n", "        \n", "        # 添加数值标签\n", "        for bar, weight in zip(bars, attention_weights):\n", "            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, \n", "                   f'{weight:.3f}', ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"🔍 注意力机制解释：\")\n", "    print(\"   • 每个词都会'关注'句子中的所有词\")\n", "    print(\"   • 注意力权重反映词与词之间的相关性\")\n", "    print(\"   • 这使模型能够捕捉长距离依赖关系\")\n", "\n", "simple_attention_demo()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 课程总结：深度学习的演进之路\n", "\n", "### 🎯 核心洞察\n", "\n", "**统一的学习框架：**\n", "- 所有模型都基于相同的数学原理\n", "- 关键在于如何设计损失函数\n", "- 不同的约束和先验知识导致不同的能力\n", "\n", "**知识注入的演进：**\n", "- **线性回归**：简单的线性关系\n", "- **DNN**：纯数据驱动的非线性建模\n", "- **PINN**：显式物理约束\n", "- **LLM**：隐式语言规律\n", "\n", "### 🚀 未来展望\n", "\n", "- **多模态融合**：文本、图像、音频的统一理解\n", "- **科学AI**：AI加速科学发现\n", "- **效率优化**：更小、更快、更节能的模型\n", "\n", "**核心公式：合适的模型 + 合适的数据 + 合适的约束 = 强大的AI能力**\n", "\n", "---\n", "\n", "**感谢大家的聆听！** 🎉"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}